{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
<div class="main__main main__main--one-column">
	<div class="main__content scroll" n:snippet="mainContent">

		<h2>
			{_'title_free_variants'}
		</h2>
		{embed $templates . '/part/box/toggle.latte', templates => $templates, props=> [
		title: $translator->translate('product_filter'),
		id: 'filter',
		icon: $templates . '/part/icons/search.svg',
		variant: 'main',
		open: false,
		classes: ['u-mb-xxs'],
		rowMainClass: 'row-main-max',
		]}
			{block content}
				{control filterForm}
			{/block}
		{/embed}

		{embed $templates . '/part/box/toggle.latte', templates => $templates, props=> [
		title: $translator->translate('Products'),
		id: 'products',
		icon: $templates . '/part/icons/book.svg',
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],
		rowMainClass: 'row-main-max',
		]}
			{block content}
				{control dataGrid}
			{/block}
		{/embed}
		<h2>
			{_'title_bulk_operation_actions'}
		</h2>
	{if $hasFilter}
		{*control productNewAction*}
		<hr>
		{*control productAddAction*}
	{/if}
	</div>

	<div class="main__content-side scroll">
		<p class="u-mb-xs">
			<a n:href="this, filterSetup=>[]" class="btn btn--full btn--grey">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/ban.svg'}
				</span>
				<span class="item-icon__text">
					<span class="grid-inline"> <span>Resetovat filtraci</span></span>
				</span>
			</span>
			</a>
		</p>
	</div>
</div>
