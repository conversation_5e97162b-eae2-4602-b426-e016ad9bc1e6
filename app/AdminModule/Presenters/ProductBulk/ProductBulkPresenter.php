<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductBulk;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\ProductBulk\Components\DataGrid\DataSourceFactory;
use App\AdminModule\Presenters\ProductBulk\Components\Filter\FilterFormFactory;
use App\AdminModule\Presenters\ProductBulk\Components\Filter\FilterForm;
use App\AdminModule\Presenters\ProductBulk\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\ProductBulk\Components\DataGrid\DataGridFactory;
use App\Model\ElasticSearch\Superadmin\Convertor\ProductVariantData;
use App\Model\Orm\EsIndex\EsIndexRepository;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Responses\JsonResponse;
use Ublaboo\DataGrid\DataSource\ArrayDataSource;

final class ProductBulkPresenter extends BasePresenter
{

	#[Persistent]
	public array $filterSetup = [];

	private BoolQuery $boolQuery;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FilterFormFactory $filterFormFactory,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly DataSourceFactory $dataSourceFactory,
		//private readonly ProductNewActionFactory $productNewActionFactory,
		//private readonly ProductAddActionFactory $productAddActionFactory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(): void
	{
		$this->fillBoolQuery();
		/*$conds = [];

		if (isset($data['erpIds']) && trim($data['erpIds']) !== '') {
			$erpIds = explode("\n", $data['erpIds']);
			if ($erpIds !== []) {
				$conds['erpIds'] = $erpIds;
			}
		}

		if (isset($data['eans']) && trim($data['eans']) !== '') {
			$eans = explode("\n", $data['eans']);
			if ($eans !== []) {
				$conds['eans'] = $eans;
			}
		}

		if (isset($data['products']) && is_array($data['products']) && count($data['products']) > 0) {
			$conds['ids'] = $data['products'];
		}

		if (isset($data['erpCategory']) && trim($data['erpCategory']) !== '') {
			$conds['erpCategory'] = $data['erpCategory'];
		}

		if ($conds !== []) {
			$this->variants = $this->variants->findBy(
				[
					ICollection::OR,
					isset($conds['erpCategory']) ? ['erpCategoryPath' => $conds['erpCategory']] : [],
					isset($conds['erpIds']) ? ['extId' => $conds['erpIds']] : [],
					isset($conds['eans']) ? ['ean' => $conds['eans']] : [],
					isset($conds['ids']) ? ['id' => $conds['ids']] : [],
				]);
		}*/
	}

	public function actionProductVariants(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$variants = $this->orm->productVariant->searchByName($searchTerm)
			->limitBy(10);

		foreach ($variants as $variant) {
			$responseData[] = ['value' => $variant->id, 'label' => $variant->erpName];
		}

		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function actionCategories(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$eshopTree = $this->orm->tree->getByUid("eshop", $this->mutationsHolder->getDefault());
		$trees = $this->orm->tree->searchByName($searchTerm, pathId: $eshopTree->id)
			->limitBy(10);

		foreach ($trees as $tree) {
			$responseData[] = ['value' => $tree->id, 'label' => $tree->name];
		}

		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function actionTags(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$tags = $this->orm->tagLocalization->searchByName($searchTerm)
			->limitBy(10);

		foreach ($tags as $tag) {
			$responseData[] = ['value' => $tag->id, 'label' => $tag->name];
		}

		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function renderDefault(): void
	{
		$this->template->hasFilter = $this->filterSetup !== [];
	}

	private function fillBoolQuery(): void
	{
		$data = $this->filterSetup;
		$b = new QueryBuilder();
		$this->boolQuery = $b->query()->bool();

		if (isset($data['eans']) && trim($data['eans']) !== '') {
			$eans = explode("\n", $data['eans']);
			if ($eans !== []) {
				$this->boolQuery->addMust(
					$b->query()->terms('filter.eans')->setTerms($eans)
				);
			}
		}
		if (isset($data['productVariants']) && is_array($data['productVariants'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('id')->setTerms(array_values($data['productVariants']))
			);
		}
		if (isset($data['categories']) && is_array($data['categories'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('filter.categories')->setTerms(array_values($data['categories']))
			);
		}
		if (isset($data['tags']) && is_array($data['tags'])) {
			$this->boolQuery->addMust(
				$b->query()->terms('bulkFilter.tagIds')->setTerms(array_values($data['tags']))
			);
		}
		if (isset($data['negate']) && (bool) $data['negate'] === true) {
			$this->boolQuery = $b->query()->bool()->addMustNot($this->boolQuery);
		}

		$this->boolQuery->addMust(
			$b->query()->term(['type' => ProductVariantData::KIND_PRODUCT_VARIANT])
		);
	}

	protected function createComponentFilterForm(): FilterForm
	{
		return $this->filterFormFactory->create($this->filterSetup);
	}

	protected function createComponentDataGrid(): DataGrid
	{
		$dataSource = new ArrayDataSource([]);
		$esIndex = $this->esIndexRepository->getSuperadminLastActive($this->mutationsHolder->getDefault());
		if ($esIndex !== null) {
			$dataSource = $this->dataSourceFactory->create($this->boolQuery, $esIndex);
		}

		return $this->dataGridFactory->create($dataSource);
	}

	/*protected function createComponentProductNewAction(): ProductNewAction
	{
		assert($this->userEntity instanceof User);
		return $this->productNewActionFactory->create($this->mutationsHolder->getDefault(), $this->variants, $this->userEntity);
	}

	protected function createComponentProductAddAction(): ProductAddAction
	{
		assert($this->userEntity instanceof User);
		return $this->productAddActionFactory->create($this->mutationsHolder->getDefault(), $this->variants, $this->userEntity);
	}*/

}
