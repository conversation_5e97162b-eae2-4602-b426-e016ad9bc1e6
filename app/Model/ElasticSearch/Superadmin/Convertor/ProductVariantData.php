<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Superadmin\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\StateRepository;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\TagType;
use Nette\Utils\Strings;

class ProductVariantData implements Convertor
{

	public const string KIND_PRODUCT_VARIANT = 'product_variant';

	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly StateRepository $stateRepository,
		private readonly TranslatorDB $translatorDB,
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{
	}

	public function convert(object $object): array
	{
		assert($object instanceof ProductVariant);

		$productVariant = $object;

		$this->mutationHolder->setMutation($this->mutationsHolder->getDefault());
		$mutation = $this->mutationHolder->getMutation();

		$product = $productVariant->product;
		$product->setMutation($mutation);
		$this->translatorDB->setMutation($mutation);

		$priceGroup = $this->priceLevelRepository->getDefault();
		$state = $this->stateRepository->getDefault($mutation);

		$prices = [];
		$codes = [];
		$eans = [];
		$prices[] = $productVariant->priceVat($mutation, $priceGroup, $state);

		if (!empty($productVariant->ean)) {
			$eans[] = (string) $productVariant->ean;
		}

		if (!empty($productVariant->extId)) {
			$codes[] = (string) $productVariant->extId;
		}

		$categories = [];
		foreach ($product->inCategories as $category) {
			$categories[] = $category->id;
		}

		$tags = [];
		foreach ($product->findActiveTags(10) as $tag) {
			$tags[] = $tag->id;
		}


		$category = null;
		foreach ($product->attachCategories as $categoryMain) {
			$categoryPath = [];
			foreach ($categoryMain->pathItems as $categoryItem) {
				if ($categoryItem->uid === Tree::UID_TITLE || $categoryItem->uid === Tree::UID_ESHOP) {
					continue;
				}
				$categoryPath[] = $categoryItem->name;
			}
			$categoryPath[] = $categoryMain->name;
			$category = implode(' > ', $categoryPath);
			break;
		}

		$availability = $productVariant->productAvailability->getAvailabilityStateText();

		$ret = [
			'id' => $productVariant->id,
			'erpCode' => $productVariant->extId,
			'name' => $productVariant->name,
			'nameSort' => ($productVariant->name !== null) ? mb_strtolower($productVariant->name) : '',
			//'nameTitle' => $product->nameTitle,
			//'description' => $product->description,
			//'annotation' => $product->annotation,
			'type' => self::KIND_PRODUCT_VARIANT,
			'category' => $category ?? '',
			'availability' => $availability,
			'productType' => $product->typeName,
			'productTypeId' => $product->productTypeId,
		];

		$ret['bulkFilter'] = [];
		$ret['bulkFilter']['languageIds'] = $this->parameterValueRepository->getRawValuesIds($product, Parameter::UID_LANGUAGE);
		$ret['bulkFilter']['tagIds'] = $tags;


		$filter = [
			'priceVat' => [
				$state->code => [
					$priceGroup->type => $prices,
				],
			],
			//Parameter::UID_MANUFACTURER => $brandParamValue,
			'categories' => $categories,
			'codes' => $codes,
			'eans' => $eans,
			'isInStock' => (int) $productVariant->isInStock,
			'publish' => $this->getPublishData($product),
			'publishDate' => [$product->publicFrom->getTimestamp(), $product->publicTo->getTimestamp()],
			'productType' => $product->typeName,
			'productTypeId' => $product->productTypeId,
			'category' => $category ?? '',
			'score' => $product->score,
			'availability' => $productVariant->productAvailability->getAvailabilityStateText(),
		];

		$ret['filter'] = $filter;

		$ret['kind'] = self::KIND_PRODUCT_VARIANT;

		return $ret;
	}

	private function getPublishData(Product $product): array
	{
		$data = [];
		foreach ($product->productLocalizations as $productLocalization) {
			if ($productLocalization->public) {
				$data[] = sprintf('%s_public', $productLocalization->mutation->langCode);
			} else {
				$data[] = sprintf('%s_hide', $productLocalization->mutation->langCode);
			}
		}

		return $data;
	}

}
